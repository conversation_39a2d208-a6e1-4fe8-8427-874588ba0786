// DNS服务器配置
const domesticNameservers = [
  "https://dns.alidns.com/dns-query",
  "https://doh.pub/dns-query",
  "https://doh.360.cn/dns-query"
];

const foreignNameservers = [
  "https://*******/dns-query",
  "https://*******/dns-query",
  "https://**************/dns-query",
  "https://**************/dns-query"
];
// DNS配置
const dnsConfig = {
  enable: true,
  listen: "0.0.0.0:1053",
  ipv6: true,
  "use-system-hosts": false,
  "cache-algorithm": "arc",
  "enhanced-mode": "fake-ip",
  "fake-ip-range": "**********/16",
  "fake-ip-filter": [
    "+.lan", "+.local",
    "+.msftconnecttest.com", "+.msftncsi.com",
    "localhost.ptlogin2.qq.com", "localhost.sec.qq.com",
    "localhost.work.weixin.qq.com"
  ],
  "default-nameserver": ["*********", "************"],
  nameserver: [...domesticNameservers, ...foreignNameservers],
  "proxy-server-nameserver": [...domesticNameservers, ...foreignNameservers],
  "nameserver-policy": {
    "geosite:private,cn,geolocation-cn": domesticNameservers,
    "geosite:google,youtube,telegram,gfw,geolocation-!cn": foreignNameservers
  }
};
// 规则集配置
const ruleProviderCommon = { type: "http", format: "yaml", interval: 86400 };

const ruleProviders = {
  reject: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt", path: "./ruleset/loyalsoldier/reject.yaml" },
  icloud: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt", path: "./ruleset/loyalsoldier/icloud.yaml" },
  apple: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt", path: "./ruleset/loyalsoldier/apple.yaml" },
  google: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt", path: "./ruleset/loyalsoldier/google.yaml" },
  proxy: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt", path: "./ruleset/loyalsoldier/proxy.yaml" },
  direct: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt", path: "./ruleset/loyalsoldier/direct.yaml" },
  private: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt", path: "./ruleset/loyalsoldier/private.yaml" },
  gfw: { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt", path: "./ruleset/loyalsoldier/gfw.yaml" },
  "tld-not-cn": { ...ruleProviderCommon, behavior: "domain", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt", path: "./ruleset/loyalsoldier/tld-not-cn.yaml" },
  telegramcidr: { ...ruleProviderCommon, behavior: "ipcidr", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt", path: "./ruleset/loyalsoldier/telegramcidr.yaml" },
  cncidr: { ...ruleProviderCommon, behavior: "ipcidr", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt", path: "./ruleset/loyalsoldier/cncidr.yaml" },
  lancidr: { ...ruleProviderCommon, behavior: "ipcidr", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt", path: "./ruleset/loyalsoldier/lancidr.yaml" },
  applications: { ...ruleProviderCommon, behavior: "classical", url: "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt", path: "./ruleset/loyalsoldier/applications.yaml" },
  openai: { ...ruleProviderCommon, behavior: "classical", url: "https://fastly.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml", path: "./ruleset/blackmatrix7/openai.yaml" },
  youtube: { ...ruleProviderCommon, behavior: "classical", url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/YouTube/YouTube.yaml", path: "./ruleset/blackmatrix7/youtube.yaml" }
};
// 规则配置
const rules = [
  "DOMAIN-SUFFIX,googleapis.cn,节点选择",
  "DOMAIN-SUFFIX,gstatic.com,节点选择",
  "DOMAIN-SUFFIX,xn--ngstr-lra8j.com,节点选择",
  "DOMAIN-SUFFIX,github.io,节点选择",
  "DOMAIN,v2rayse.com,节点选择",
  "RULE-SET,openai,常用服务",
  "RULE-SET,applications,全局直连",
  "RULE-SET,private,全局直连",
  "RULE-SET,reject,拦截过滤",
  "RULE-SET,icloud,常用服务",
  "RULE-SET,apple,常用服务",
  "RULE-SET,google,常用服务",
  "RULE-SET,youtube,台日韩",
  "RULE-SET,proxy,节点选择",
  "RULE-SET,gfw,节点选择",
  "RULE-SET,tld-not-cn,节点选择",
  "RULE-SET,direct,全局直连",
  "RULE-SET,lancidr,全局直连,no-resolve",
  "RULE-SET,cncidr,全局直连,no-resolve",
  "RULE-SET,telegramcidr,常用服务,no-resolve",
  "GEOIP,LAN,全局直连,no-resolve",
  "GEOIP,CN,全局直连,no-resolve",
  "MATCH,漏网之鱼"
];
// 代理组通用配置
const groupBaseOption = {
  interval: 300,
  timeout: 3000,
  url: "https://www.google.com/generate_204",
  lazy: true,
  "max-failed-times": 3,
  hidden: false
};

// 程序入口
function main(config) {
const proxyCount = config?.proxies?.length ?? 0;
const proxyProviderCount =
typeof config?.["proxy-providers"] === "object" ? Object.keys(config["proxy-providers"]).length : 0;
if (proxyCount === 0 && proxyProviderCount === 0) {
throw new Error("配置文件中未找到任何代理");
}

// 覆盖原配置中DNS配置
config["dns"] = dnsConfig;

// 代理组配置
config["proxy-groups"] = [
  { ...groupBaseOption, name: "节点选择", type: "select", proxies: ["延迟选优", "故障转移", "负载均衡"], "include-all": true, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/adjust.svg" },
  { ...groupBaseOption, name: "延迟选优", type: "url-test", "include-all": true, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/speed.svg" },
  { ...groupBaseOption, name: "故障转移", type: "fallback", "include-all": true, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/ambulance.svg" },
  { ...groupBaseOption, name: "负载均衡", type: "load-balance", strategy: "round-robin", "include-all": true, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/balance.svg" },
  { ...groupBaseOption, name: "常用服务", type: "select", proxies: ["节点选择", "延迟选优", "故障转移", "负载均衡", "全局直连"], "include-all": true, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/google.svg" },
  { ...groupBaseOption, name: "台日韩", type: "url-test", filter: "日本|jp|韩国|kr|台湾|tw", icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/google.svg" },
  { ...groupBaseOption, name: "全局直连", type: "select", proxies: ["DIRECT", "节点选择"], icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/link.svg" },
  { ...groupBaseOption, name: "拦截过滤", type: "select", proxies: ["REJECT", "DIRECT"], icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/block.svg" },
  { ...groupBaseOption, name: "漏网之鱼", type: "select", proxies: ["节点选择", "全局直连"], "include-all": true, icon: "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/fish.svg" }
];

// 覆盖原配置中的规则
config["rule-providers"] = ruleProviders;
config["rules"] = rules;

// 返回修改后的配置
return config;
}